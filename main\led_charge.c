#include "led_charge.h"
#include "esp_log.h"

#define GPIO_POWER_SOURCE GPIO_NUM_10  // 供电来源 -> 低电平:外部供电   高电平:电池供电
#define GPIO_CHARGE_STATUS GPIO_NUM_18 // 显示充电状态 -> 闪烁:充电中  常亮:充电完成
#define BLINK_INTERVAL_MS 500          // 闪烁间隔 (毫秒)

static const char *TAG = "LED_CHARGE";
static bool led_initialized = false;
static led_state_t current_led_state = LED_STATE_OFF;
static TaskHandle_t charge_task_handle = NULL;

esp_err_t led_charge_init(void)
{
    if (led_initialized)
    {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    // 配置GPIO为输出模式
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,     // 禁用中断
        .mode = GPIO_MODE_OUTPUT,           // 输出模式
        .pin_bit_mask = (1ULL << LED_GPIO), // LED_GPIO
        .pull_down_en = 0,                  // 禁用下拉
        .pull_up_en = 0,                    // 禁用上拉
    };

    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始状态设为低电平（LED关闭）
    ret = gpio_set_level(LED_GPIO, LED_STATE_OFF);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set initial LED state: %s", esp_err_to_name(ret));
        return ret;
    }
    current_led_state = LED_STATE_OFF;

    led_initialized = true;
    ESP_LOGI(TAG, "GPIO%d initialized as LED output", LED_GPIO);

    return ESP_OK;
}

/**
 * @brief 显示充电状态
 */
static void led_charge_task_callback(void *pvParameters)
{
    while (1)
    {

        // 延时
        vTaskDelay(pdMS_TO_TICKS(BLINK_INTERVAL_MS));
    }
}

/**
 * @brief 创建充电状态任务
 */
esp_err_t led_charge_task()
{
    if (!led_initialized)
    {
        ESP_LOGE(TAG, "led_charge not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (charge_task_handle != NULL)
    {
        ESP_LOGW(TAG, "led_charge task already running");
        return ESP_OK;
    }

    blink_interval = blink_interval_ms;

    BaseType_t task_ret = xTaskCreate(
        led_charge_task_callback, // 任务函数
        "led_charge_task",        // 任务名称
        2048,                     // 堆栈大小
        NULL,                     // 任务参数
        task_priority,            // 任务优先级
        &charge_task_handle       // 任务句柄
    );

    if (task_ret != pdPASS)
    {
        ESP_LOGE(TAG, "failed to create led_charge task");
        charge_task_handle = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "led_charge task started with %lu ms interval", blink_interval);
    return ESP_OK;
}