/*
 * Device LED Control Header
 * LED GPIO control and blink functionality
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /* LED状态定义 */
    typedef enum
    {
        LED_STATE_OFF = 0, /* LED关闭 */
        LED_STATE_ON = 1   /* LED开启 */
    } led_state_t;

    /**
     * @brief 初始化LED GPIO

     *
     * @return
     *     - ESP_OK: 初始化成功
     *     - 其他: 初始化失败的错误码
     */
    esp_err_t led_charge_init(void);

    /**
     * @brief 创建LED闪烁任务
     *
     * 创建一个FreeRTOS任务，让LED以指定间隔闪烁
     *
     * @param task_priority 任务优先级
     * @return
     *     - ESP_OK: 任务创建成功
     *     - ESP_FAIL: 任务创建失败
     */
    esp_err_t led_charge_task();

#ifdef __cplusplus
}
#endif