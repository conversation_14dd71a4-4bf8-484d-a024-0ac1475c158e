# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
ota_0,    app,  ota_0,   0x10000, 0x1C0000,
ota_1,    app,  ota_1,   0x1D0000, 0x1C0000,
otadata,  data, ota,     0x390000, 0x2000,
storage,  data, spiffs,  0x392000, 0x6E000,
